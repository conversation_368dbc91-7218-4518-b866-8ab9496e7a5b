<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMS Messaging Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sms-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        .sms-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .quick-send {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 10px;
        }
        .template-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 10px;
        }
        .character-count {
            font-size: 0.8em;
            color: #666;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-6 mb-0">
                    <i class="fas fa-sms text-primary"></i>
                    SMS Messaging Dashboard
                </h1>
                <p class="text-muted">Send SMS messages to your clients and customers</p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-paper-plane fa-2x mb-2"></i>
                        <h3 class="mb-1" id="total-sent">0</h3>
                        <p class="mb-0">Total Sent</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3 class="mb-1" id="delivered">0</h3>
                        <p class="mb-0">Delivered</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h3 class="mb-1" id="failed">0</h3>
                        <p class="mb-0">Failed</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h3 class="mb-1" id="pending">0</h3>
                        <p class="mb-0">Pending</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Quick Send SMS -->
            <div class="col-lg-8 mb-4">
                <div class="card quick-send h-100">
                    <div class="card-header border-0">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt"></i>
                            Quick Send SMS
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="quick-sms-form">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Recipient Type</label>
                                    <select class="form-select" id="recipient-type" required>
                                        <option value="">Select Type</option>
                                        <option value="Customer">Customer</option>
                                        <option value="Supplier">Supplier</option>
                                        <option value="Custom">Custom</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone-number" 
                                           placeholder="+1234567890" required>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Recipient Name</label>
                                    <input type="text" class="form-control" id="recipient-name" 
                                           placeholder="Enter recipient name" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Template (Optional)</label>
                                    <select class="form-select" id="template-select">
                                        <option value="">No Template</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Message Content</label>
                                <textarea class="form-control" id="message-content" rows="4" 
                                          placeholder="Type your message here..." required></textarea>
                                <div class="character-count mt-1">
                                    <span id="char-count">0</span> characters 
                                    (<span id="sms-count">0</span> SMS)
                                </div>
                            </div>
                            <button type="submit" class="btn btn-light btn-lg">
                                <i class="fas fa-paper-plane"></i>
                                Send SMS
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Templates & Recent Messages -->
            <div class="col-lg-4">
                <!-- Templates -->
                <div class="card template-card mb-4">
                    <div class="card-header border-0">
                        <h6 class="mb-0">
                            <i class="fas fa-file-text"></i>
                            Quick Templates
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="template-list">
                            <div class="template-item mb-2 p-2 bg-white bg-opacity-25 rounded" 
                                 data-template="Welcome Message">
                                <small class="fw-bold">Welcome Message</small>
                                <div class="small">Welcome to {company_name}...</div>
                            </div>
                            <div class="template-item mb-2 p-2 bg-white bg-opacity-25 rounded" 
                                 data-template="Payment Reminder">
                                <small class="fw-bold">Payment Reminder</small>
                                <div class="small">Your payment of {amount}...</div>
                            </div>
                        </div>
                        <button class="btn btn-light btn-sm mt-2" onclick="manageTemplates()">
                            <i class="fas fa-cog"></i> Manage Templates
                        </button>
                    </div>
                </div>

                <!-- Recent Messages -->
                <div class="card sms-card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-history"></i>
                            Recent Messages
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="recent-messages">
                            <div class="message-item mb-2 p-2 border-start border-3 border-success">
                                <small class="fw-bold">John Doe</small>
                                <div class="small text-muted">Welcome to our service...</div>
                                <div class="small text-success">
                                    <i class="fas fa-check"></i> Delivered
                                </div>
                            </div>
                            <div class="message-item mb-2 p-2 border-start border-3 border-warning">
                                <small class="fw-bold">Jane Smith</small>
                                <div class="small text-muted">Your payment reminder...</div>
                                <div class="small text-warning">
                                    <i class="fas fa-clock"></i> Pending
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-outline-primary btn-sm mt-2" onclick="viewAllMessages()">
                            <i class="fas fa-list"></i> View All
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Character counter
        document.getElementById('message-content').addEventListener('input', function() {
            const content = this.value;
            const charCount = content.length;
            const smsCount = Math.ceil(charCount / 160) || 0;
            
            document.getElementById('char-count').textContent = charCount;
            document.getElementById('sms-count').textContent = smsCount;
        });

        // Template selection
        document.getElementById('template-select').addEventListener('change', function() {
            const templateName = this.value;
            if (templateName) {
                // In a real implementation, this would fetch the template content
                loadTemplate(templateName);
            }
        });

        // Template item clicks
        document.querySelectorAll('.template-item').forEach(item => {
            item.addEventListener('click', function() {
                const templateName = this.dataset.template;
                document.getElementById('template-select').value = templateName;
                loadTemplate(templateName);
            });
        });

        // Form submission
        document.getElementById('quick-sms-form').addEventListener('submit', function(e) {
            e.preventDefault();
            sendSMS();
        });

        function loadTemplate(templateName) {
            // Mock template content - in real implementation, this would be an API call
            const templates = {
                'Welcome Message': 'Welcome to {company_name}, {customer_name}! Thank you for joining us.',
                'Payment Reminder': 'Dear {customer_name}, your payment of {amount} is due on {due_date}.'
            };
            
            if (templates[templateName]) {
                document.getElementById('message-content').value = templates[templateName];
                document.getElementById('message-content').dispatchEvent(new Event('input'));
            }
        }

        function sendSMS() {
            const formData = {
                recipientType: document.getElementById('recipient-type').value,
                phoneNumber: document.getElementById('phone-number').value,
                recipientName: document.getElementById('recipient-name').value,
                messageContent: document.getElementById('message-content').value,
                template: document.getElementById('template-select').value
            };

            // Mock SMS sending - in real implementation, this would be an API call
            console.log('Sending SMS:', formData);
            
            // Show success message
            alert('SMS sent successfully! (This is a demo)');
            
            // Reset form
            document.getElementById('quick-sms-form').reset();
            document.getElementById('char-count').textContent = '0';
            document.getElementById('sms-count').textContent = '0';
        }

        function manageTemplates() {
            alert('Template management would open here in the full implementation');
        }

        function viewAllMessages() {
            alert('Full message list would open here in the full implementation');
        }

        // Load initial data (mock)
        function loadDashboardData() {
            // Mock statistics
            document.getElementById('total-sent').textContent = '1,234';
            document.getElementById('delivered').textContent = '1,180';
            document.getElementById('failed').textContent = '12';
            document.getElementById('pending').textContent = '42';
        }

        // Initialize dashboard
        loadDashboardData();
    </script>
</body>
</html>
