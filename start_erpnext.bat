@echo off
echo Starting ERPNext Development Server...
echo.
echo This will open ERPNext in WSL Ubuntu
echo Access ERPNext at: http://localhost:8000
echo Username: Administrator
echo Password: admin123
echo.
echo Press Ctrl+C to stop the server when it's running
echo.
pause

echo Checking for processes using port 8000...
wsl -d Ubuntu -e bash -c "sudo lsof -ti:8000 | xargs -r sudo kill -9"
echo Killed any processes using port 8000

echo Starting ERPNext...
wsl -d Ubuntu -e bash -c "cd /home/<USER>/frappe-bench && export PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:$PATH && export GIT_PYTHON_GIT_EXECUTABLE=/usr/bin/git && bench serve --port 8000"

echo.
echo ERPNext server has stopped or failed to start.
echo Check the error messages above.
pause
