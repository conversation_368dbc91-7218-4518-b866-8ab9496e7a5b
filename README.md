# ERPNext Development Environment

This directory contains everything you need to set up and work with ERPNext in development mode.

## 🚀 Quick Start

### Option 1: Automated Installation (Recommended)
```bash
# Make the script executable
chmod +x install_erpnext.sh

# Run the installation script
./install_erpnext.sh
```

### Option 2: Manual Installation
Follow the detailed guide in `ERPNext_Development_Guide.md`

## 📁 Files in This Directory

- **`ERPNext_Development_Guide.md`** - Comprehensive installation and development guide
- **`install_erpnext.sh`** - Automated installation script
- **`README.md`** - This file
- **`frappe-bench/`** - Symbolic link to your ERPNext installation (created after installation)

## 🔗 Access Your ERPNext

After installation, access your ERPNext instance at:
- **URL**: http://localhost:8000
- **Username**: Administrator
- **Password**: admin123

## 🛠️ Quick Commands

Once installed, you can use these convenient aliases:

```bash
# Start ERPNext development server
erpnext-start

# Open ERPNext console for development
erpnext-console

# Run database migrations
erpnext-migrate

# Build frontend assets
erpnext-build

# Clear application cache
erpnext-clear-cache

# Create backup
erpnext-backup
```

## 📖 What's Installed

- **Frappe Framework v15** - The underlying framework
- **ERPNext v15** - Complete ERP system
- **MariaDB** - Database server
- **Redis** - Caching and session storage
- **Development tools** - All necessary dependencies

## 🔧 Development Features

- ✅ Developer mode enabled
- ✅ Live reload on file changes
- ✅ Debug mode active
- ✅ Scheduler enabled
- ✅ Direct file access from Windows

## 📚 Documentation

For detailed information, see:
- `ERPNext_Development_Guide.md` - Complete setup and development guide
- [Official ERPNext Documentation](https://docs.erpnext.com)
- [Frappe Framework Documentation](https://frappeframework.com/docs)

## 🆘 Need Help?

1. Check the troubleshooting section in `ERPNext_Development_Guide.md`
2. Visit the [ERPNext Forum](https://discuss.erpnext.com)
3. Check the [GitHub Issues](https://github.com/frappe/erpnext/issues)

## 🔄 Starting/Stopping ERPNext

### Start ERPNext
```bash
cd ~/frappe-bench
./start_erpnext.sh
```

### Stop ERPNext
Press `Ctrl+C` in the terminal where ERPNext is running

### Alternative Start Method
```bash
cd ~/frappe-bench
bench serve --port 8000
```

## 🎯 Next Steps

1. **Explore ERPNext**: Login and familiarize yourself with the interface
2. **Read the Guide**: Go through `ERPNext_Development_Guide.md` for detailed information
3. **Create Custom Apps**: Start building your own applications
4. **Join the Community**: Connect with other ERPNext developers

## 📝 System Information

- **Installation Location**: `/home/<USER>/frappe-bench` (in WSL)
- **Site Name**: `erpnext.local`
- **Database**: MariaDB with root password `codeman1`
- **Cache**: Redis server
- **Environment**: Development mode with live reload

## ⚠️ Important Notes

- This is a **development environment** - not suitable for production
- Default passwords are used - change them for any serious development
- The installation is optimized for learning and customization
- All files are accessible from Windows through the symbolic link

---

**Happy Developing! 🎉**

For any issues or questions, refer to the comprehensive guide in `ERPNext_Development_Guide.md`.
