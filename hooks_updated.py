app_name = "sms_messaging"
app_title = "SMS Messaging"
app_publisher = "nick"
app_description = "Message clients using SMS integration straight through dashboard"
app_email = "<EMAIL>"
app_license = "mit"

# Apps
# ------------------

# required_apps = []

# Each item in the list will be shown as an app in the apps page
add_to_apps_screen = [
    {
        "name": "sms_messaging",
        "logo": "/assets/sms_messaging/logo.png",
        "title": "SMS Messaging",
        "route": "/app/sms-message",
        "has_permission": "sms_messaging.api.has_app_permission"
    }
]

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
app_include_css = "/assets/sms_messaging/css/sms_messaging.css"
app_include_js = "/assets/sms_messaging/js/sms_messaging.js"

# include js, css files in header of web template
# web_include_css = "/assets/sms_messaging/css/sms_messaging.css"
# web_include_js = "/assets/sms_messaging/js/sms_messaging.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "sms_messaging/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
doctype_js = {
    "Customer": "public/js/customer.js",
    "Supplier": "public/js/supplier.js"
}
# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Svg Icons
# ------------------
# include app icons in desk
# app_include_icons = "sms_messaging/public/icons.svg"

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
#       "Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
jinja = {
    "methods": "sms_messaging.utils.jinja_methods",
    "filters": "sms_messaging.utils.jinja_filters"
}

# Installation
# ------------

after_install = "sms_messaging.install.after_install"

# Uninstallation
# ------------

# before_uninstall = "sms_messaging.uninstall.before_uninstall"
# after_uninstall = "sms_messaging.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "sms_messaging.utils.before_app_install"
# after_app_install = "sms_messaging.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "sms_messaging.utils.before_app_uninstall"
# after_app_uninstall = "sms_messaging.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

notification_config = "sms_messaging.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
#       "Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
#       "Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
#       "ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

doc_events = {
    "Customer": {
        "after_insert": "sms_messaging.events.customer.send_welcome_sms"
    },
    "Sales Invoice": {
        "on_submit": "sms_messaging.events.sales_invoice.send_invoice_sms"
    },
    "Payment Entry": {
        "on_submit": "sms_messaging.events.payment_entry.send_payment_confirmation_sms"
    }
}

# Scheduled Tasks
# ---------------

scheduler_events = {
    "hourly": [
        "sms_messaging.tasks.retry_failed_messages",
        "sms_messaging.tasks.update_delivery_status"
    ],
    "daily": [
        "sms_messaging.tasks.cleanup_old_messages",
        "sms_messaging.tasks.send_daily_summary"
    ],
}

# Testing
# -------

# before_tests = "sms_messaging.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
#       "frappe.desk.doctype.event.event.get_events": "sms_messaging.event.get_events"
# }
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
override_doctype_dashboards = {
    "Customer": "sms_messaging.dashboard.customer.get_dashboard_data"
}

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["sms_messaging.utils.before_request"]
# after_request = ["sms_messaging.utils.after_request"]

# Job Events
# ----------
# before_job = ["sms_messaging.utils.before_job"]
# after_job = ["sms_messaging.utils.after_job"]

# User Data Protection
# --------------------

user_data_fields = [
    {
        "doctype": "SMS Message",
        "filter_by": "sent_by",
        "redact_fields": ["phone_number", "message_content"],
        "partial": 1,
    }
]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
#       "sms_messaging.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
#       "Logging DocType Name": 30  # days to retain logs
# }

# Website Route Rules
# -------------------

website_route_rules = [
    {"from_route": "/sms-webhook/<path:path>", "to_route": "sms_messaging.api.handle_delivery_status"},
]

# Boot Session
# ------------

boot_session = "sms_messaging.boot.boot_session"

# Standard Portal Items
# ---------------------

standard_portal_menu_items = [
    {
        "title": "SMS Messages",
        "route": "/sms-messages",
        "reference_doctype": "SMS Message",
        "role": "SMS User"
    }
]

# Custom Fields
# -------------

# Add custom fields to existing doctypes
custom_fields = {
    "Customer": [
        {
            "fieldname": "sms_notifications",
            "label": "SMS Notifications",
            "fieldtype": "Check",
            "default": 1,
            "insert_after": "mobile_no"
        }
    ],
    "Supplier": [
        {
            "fieldname": "sms_notifications",
            "label": "SMS Notifications", 
            "fieldtype": "Check",
            "default": 1,
            "insert_after": "mobile_no"
        }
    ]
}

# Fixtures
# --------

fixtures = [
    {
        "doctype": "Custom Role",
        "filters": [
            [
                "name",
                "in",
                [
                    "SMS User",
                    "SMS Manager"
                ]
            ]
        ]
    },
    {
        "doctype": "SMS Template",
        "filters": [
            [
                "enabled",
                "=",
                1
            ]
        ]
    }
]
