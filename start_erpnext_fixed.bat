@echo off
echo Starting ERPNext Development Server...
echo.

REM Get WSL IP address
for /f "tokens=2 delims=: " %%i in ('wsl hostname -I') do set WSL_IP=%%i
set WSL_IP=%WSL_IP: =%

echo WSL IP Address: %WSL_IP%
echo.

REM Remove existing port proxy if it exists
netsh interface portproxy delete v4tov4 listenport=8000 listenaddress=127.0.0.1 >nul 2>&1

REM Add port forwarding from Windows localhost to WSL
echo Setting up port forwarding...
netsh interface portproxy add v4tov4 listenport=8000 listenaddress=127.0.0.1 connectport=8000 connectaddress=%WSL_IP%

if %errorlevel% neq 0 (
    echo Failed to set up port forwarding. You may need to run as Administrator.
    echo Try accessing ERPNext directly at: http://%WSL_IP%:8000
    pause
    exit /b 1
)

echo Port forwarding configured successfully!
echo.

REM Kill any existing processes on port 8000
echo Stopping any existing ERPNext processes...
wsl -e bash -c "sudo lsof -ti:8000 | xargs -r sudo kill -9" >nul 2>&1

echo Starting ERPNext server...
echo.
echo ERPNext will be available at:
echo   - http://localhost:8000
echo   - http://127.0.0.1:8000
echo   - http://%WSL_IP%:8000
echo.
echo Username: Administrator
echo Password: admin123
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start ERPNext
wsl -e bash -c "export PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:$PATH && cd /home/<USER>/frappe-bench && bench serve --port 8000"

echo.
echo ERPNext server has stopped.
echo.

REM Clean up port forwarding
echo Cleaning up port forwarding...
netsh interface portproxy delete v4tov4 listenport=8000 listenaddress=127.0.0.1 >nul 2>&1

pause
