# Copyright (c) 2024, nick and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def after_install():
    """Setup SMS Messaging app after installation"""
    try:
        # Create custom roles
        create_custom_roles()
        
        # Setup default SMS settings
        setup_default_sms_settings()
        
        # Create common SMS templates
        create_common_templates()
        
        # Add custom fields to existing doctypes
        add_custom_fields()
        
        frappe.db.commit()
        
        print("SMS Messaging app installed successfully!")
        
    except Exception as e:
        frappe.log_error(f"Error during SMS Messaging installation: {str(e)}", "SMS Install Error")
        print(f"Installation error: {str(e)}")


def create_custom_roles():
    """Create custom roles for SMS messaging"""
    roles = [
        {
            "role_name": "SMS User",
            "description": "Can send and view SMS messages"
        },
        {
            "role_name": "SMS Manager", 
            "description": "Can manage SMS settings and templates"
        }
    ]
    
    for role_data in roles:
        if not frappe.db.exists("Role", role_data["role_name"]):
            role = frappe.get_doc({
                "doctype": "Role",
                "role_name": role_data["role_name"],
                "description": role_data["description"]
            })
            role.insert()
            print(f"Created role: {role_data['role_name']}")


def setup_default_sms_settings():
    """Setup default SMS settings"""
    try:
        # Check if SMS Settings already exists
        if frappe.db.exists("SMS Settings", "SMS Settings"):
            return
            
        settings = frappe.get_doc({
            "doctype": "SMS Settings",
            "enabled": 0,
            "provider": "Twilio",
            "rate_limit_per_minute": 60,
            "enable_delivery_reports": 1,
            "retry_failed_messages": 1,
            "max_retry_attempts": 3,
            "aws_region": "us-east-1"
        })
        settings.insert()
        print("Created default SMS settings")
        
    except Exception as e:
        frappe.log_error(f"Error creating SMS settings: {str(e)}", "SMS Install Error")


def create_common_templates():
    """Create common SMS templates"""
    templates = [
        {
            "template_name": "Welcome Message",
            "content": "Welcome to {company_name}, {customer_name}! Thank you for joining us. For support, call {support_phone}.",
            "message_type": "Welcome",
            "category": "Customer Onboarding",
            "description": "Welcome message for new customers",
            "enabled": 1
        },
        {
            "template_name": "Order Confirmation",
            "content": "Your order #{order_id} has been confirmed. Total: {amount}. Expected delivery: {delivery_date}.",
            "message_type": "Transactional",
            "category": "Orders",
            "description": "Order confirmation message",
            "enabled": 1
        },
        {
            "template_name": "Payment Reminder",
            "content": "Dear {customer_name}, your payment of {amount} for invoice #{invoice_id} is due on {due_date}.",
            "message_type": "Reminder",
            "category": "Payments",
            "description": "Payment reminder message",
            "enabled": 1
        },
        {
            "template_name": "Payment Confirmation",
            "content": "Thank you {customer_name}! We have received your payment of {amount} for invoice #{invoice_id}.",
            "message_type": "Transactional",
            "category": "Payments",
            "description": "Payment confirmation message",
            "enabled": 1
        },
        {
            "template_name": "OTP Verification",
            "content": "Your OTP for {service_name} is {otp_code}. Valid for {validity_minutes} minutes. Do not share this code.",
            "message_type": "OTP",
            "category": "Security",
            "description": "OTP verification message",
            "enabled": 1
        }
    ]
    
    for template_data in templates:
        if not frappe.db.exists("SMS Template", template_data["template_name"]):
            template = frappe.get_doc({
                "doctype": "SMS Template",
                **template_data
            })
            template.insert()
            print(f"Created template: {template_data['template_name']}")


def add_custom_fields():
    """Add custom fields to existing doctypes"""
    try:
        # Add SMS notification field to Customer
        if not frappe.db.exists("Custom Field", {"dt": "Customer", "fieldname": "sms_notifications"}):
            custom_field = frappe.get_doc({
                "doctype": "Custom Field",
                "dt": "Customer",
                "fieldname": "sms_notifications",
                "label": "SMS Notifications",
                "fieldtype": "Check",
                "default": "1",
                "insert_after": "mobile_no"
            })
            custom_field.insert()
            print("Added SMS notifications field to Customer")
            
        # Add SMS notification field to Supplier
        if not frappe.db.exists("Custom Field", {"dt": "Supplier", "fieldname": "sms_notifications"}):
            custom_field = frappe.get_doc({
                "doctype": "Custom Field",
                "dt": "Supplier",
                "fieldname": "sms_notifications",
                "label": "SMS Notifications",
                "fieldtype": "Check",
                "default": "1",
                "insert_after": "mobile_no"
            })
            custom_field.insert()
            print("Added SMS notifications field to Supplier")
            
    except Exception as e:
        frappe.log_error(f"Error adding custom fields: {str(e)}", "SMS Install Error")


def setup_permissions():
    """Setup permissions for SMS doctypes"""
    try:
        # SMS Message permissions
        sms_message_perms = [
            {
                "role": "SMS User",
                "perms": {
                    "read": 1,
                    "write": 1,
                    "create": 1,
                    "submit": 1,
                    "export": 1,
                    "print": 1,
                    "email": 1
                }
            },
            {
                "role": "SMS Manager",
                "perms": {
                    "read": 1,
                    "write": 1,
                    "create": 1,
                    "submit": 1,
                    "cancel": 1,
                    "delete": 1,
                    "export": 1,
                    "print": 1,
                    "email": 1,
                    "report": 1
                }
            }
        ]
        
        # SMS Template permissions
        sms_template_perms = [
            {
                "role": "SMS User",
                "perms": {
                    "read": 1,
                    "export": 1
                }
            },
            {
                "role": "SMS Manager",
                "perms": {
                    "read": 1,
                    "write": 1,
                    "create": 1,
                    "delete": 1,
                    "export": 1,
                    "print": 1,
                    "email": 1
                }
            }
        ]
        
        # Apply permissions
        for perm_data in sms_message_perms:
            add_permission("SMS Message", perm_data["role"], perm_data["perms"])
            
        for perm_data in sms_template_perms:
            add_permission("SMS Template", perm_data["role"], perm_data["perms"])
            
        print("Setup permissions for SMS doctypes")
        
    except Exception as e:
        frappe.log_error(f"Error setting up permissions: {str(e)}", "SMS Install Error")


def add_permission(doctype, role, perms):
    """Add permission for a doctype and role"""
    try:
        if not frappe.db.exists("Custom DocPerm", {"parent": doctype, "role": role}):
            doc_perm = frappe.get_doc({
                "doctype": "Custom DocPerm",
                "parent": doctype,
                "parenttype": "DocType",
                "parentfield": "permissions",
                "role": role,
                **perms
            })
            doc_perm.insert()
            
    except Exception as e:
        frappe.log_error(f"Error adding permission for {doctype} - {role}: {str(e)}", "SMS Install Error")


def create_workspace():
    """Create SMS Messaging workspace"""
    try:
        if frappe.db.exists("Workspace", "SMS Messaging"):
            return
            
        workspace = frappe.get_doc({
            "doctype": "Workspace",
            "title": "SMS Messaging",
            "icon": "message-circle",
            "indicator_color": "blue",
            "is_standard": 0,
            "public": 1,
            "charts": [],
            "shortcuts": [
                {
                    "type": "DocType",
                    "label": "SMS Message",
                    "doc_view": "List",
                    "link_to": "SMS Message",
                    "icon": "message-square"
                },
                {
                    "type": "DocType", 
                    "label": "SMS Template",
                    "doc_view": "List",
                    "link_to": "SMS Template",
                    "icon": "file-text"
                },
                {
                    "type": "DocType",
                    "label": "SMS Settings",
                    "doc_view": "Form",
                    "link_to": "SMS Settings",
                    "icon": "settings"
                }
            ],
            "cards": []
        })
        workspace.insert()
        print("Created SMS Messaging workspace")
        
    except Exception as e:
        frappe.log_error(f"Error creating workspace: {str(e)}", "SMS Install Error")
